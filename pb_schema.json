[{"id": "_pb_users_auth_", "listRule": "id = @request.auth.id", "viewRule": "id = @request.auth.id", "createRule": "", "updateRule": "id = @request.auth.id", "deleteRule": "id = @request.auth.id", "name": "users", "type": "auth", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cost": 0, "hidden": true, "id": "password901924565", "max": 0, "min": 8, "name": "password", "pattern": "", "presentable": false, "required": true, "system": true, "type": "password"}, {"autogeneratePattern": "[a-zA-Z0-9]{50}", "hidden": true, "id": "text2504183744", "max": 60, "min": 30, "name": "<PERSON><PERSON><PERSON>", "pattern": "", "presentable": false, "primaryKey": false, "required": true, "system": true, "type": "text"}, {"exceptDomains": null, "hidden": false, "id": "email3885137012", "name": "email", "onlyDomains": null, "presentable": false, "required": true, "system": true, "type": "email"}, {"hidden": false, "id": "bool1547992806", "name": "emailVisibility", "presentable": false, "required": false, "system": true, "type": "bool"}, {"hidden": false, "id": "bool256245529", "name": "verified", "presentable": false, "required": false, "system": true, "type": "bool"}, {"autogeneratePattern": "", "hidden": false, "id": "text1579384326", "max": 255, "min": 0, "name": "name", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "file376926767", "maxSelect": 1, "maxSize": 0, "mimeTypes": ["image/jpeg", "image/png", "image/svg+xml", "image/gif", "image/webp"], "name": "avatar", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": null, "type": "file"}, {"hidden": false, "id": "number2858029454", "max": null, "min": null, "name": "tokens", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": ["CREATE UNIQUE INDEX `idx_tokenKey__pb_users_auth_` ON `users` (`tokenKey`)", "CREATE UNIQUE INDEX `idx_email__pb_users_auth_` ON `users` (`email`) WHERE `email` != ''"], "system": false, "authRule": "", "manageRule": null, "authAlert": {"enabled": true, "emailTemplate": {"subject": "Login from a new location", "body": "<p>Hello,</p>\n<p>We noticed a login to your {APP_NAME} account from a new location.</p>\n<p>If this was you, you may disregard this email.</p>\n<p><strong>If this wasn't you, you should immediately change your {APP_NAME} account password to revoke access from all other locations.</strong></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}}, "oauth2": {"mappedFields": {"id": "", "name": "name", "username": "", "avatarURL": "avatar"}, "enabled": true}, "passwordAuth": {"enabled": true, "identityFields": ["email"]}, "mfa": {"enabled": false, "duration": 1800, "rule": ""}, "otp": {"enabled": false, "duration": 180, "length": 8, "emailTemplate": {"subject": "OTP for {APP_NAME}", "body": "<p>Hello,</p>\n<p>Your one-time password is: <strong>{OTP}</strong></p>\n<p><i>If you didn't ask for the one-time password, you can ignore this email.</i></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}}, "authToken": {"duration": 604800}, "passwordResetToken": {"duration": 1800}, "emailChangeToken": {"duration": 1800}, "verificationToken": {"duration": 259200}, "fileToken": {"duration": 180}, "verificationTemplate": {"subject": "Verify your {APP_NAME} email", "body": "<p>Hello,</p>\n<p>Thank you for joining us at {APP_NAME}.</p>\n<p>Click on the button below to verify your email address.</p>\n<p>\n  <a class=\"btn\" href=\"{APP_URL}/_/#/auth/confirm-verification/{TOKEN}\" target=\"_blank\" rel=\"noopener\">Verify</a>\n</p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}, "resetPasswordTemplate": {"subject": "Reset your {APP_NAME} password", "body": "<p>Hello,</p>\n<p>Click on the button below to reset your password.</p>\n<p>\n  <a class=\"btn\" href=\"{APP_URL}/_/#/auth/confirm-password-reset/{TOKEN}\" target=\"_blank\" rel=\"noopener\">Reset password</a>\n</p>\n<p><i>If you didn't ask to reset your password, you can ignore this email.</i></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}, "confirmEmailChangeTemplate": {"subject": "Confirm your {APP_NAME} new email address", "body": "<p>Hello,</p>\n<p>Click on the button below to confirm your new email address.</p>\n<p>\n  <a class=\"btn\" href=\"{APP_URL}/_/#/auth/confirm-email-change/{TOKEN}\" target=\"_blank\" rel=\"noopener\">Confirm new email</a>\n</p>\n<p><i>If you didn't ask to change your email address, you can ignore this email.</i></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}}, {"id": "pbc_102186087", "listRule": null, "viewRule": null, "createRule": null, "updateRule": null, "deleteRule": null, "name": "generation_requests", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "_pb_users_auth_", "hidden": false, "id": "relation2375276105", "maxSelect": 1, "minSelect": 0, "name": "user", "presentable": true, "required": true, "system": false, "type": "relation"}, {"hidden": false, "id": "json2862543216", "maxSize": 5242880, "name": "jsondata", "presentable": false, "required": false, "system": false, "type": "json"}, {"hidden": false, "id": "select2063623452", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["pending", "completed", "failed", "stalled", "canceled"]}, {"autogeneratePattern": "", "hidden": false, "id": "text3616895705", "max": 0, "min": 0, "name": "model", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text4055988317", "max": 0, "min": 0, "name": "replicate_id", "pattern": "", "presentable": true, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_1393567642", "hidden": false, "id": "relation1614760762", "maxSelect": 999, "minSelect": 0, "name": "output_files", "presentable": true, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": ["CREATE INDEX `idx_keOzKDTWjJ` ON `generation_requests` (`user`)", "CREATE INDEX `idx_FEKdeRNXrh` ON `generation_requests` (`status`)", "CREATE INDEX `idx_fn2ieNvYFJ` ON `generation_requests` (`replicate_id`)"], "system": false}, {"id": "pbc_1393567642", "listRule": null, "viewRule": null, "createRule": null, "updateRule": null, "deleteRule": null, "name": "output_result_files", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"hidden": false, "id": "file2359244304", "maxSelect": 1, "maxSize": 734003200, "mimeTypes": [], "name": "file", "presentable": true, "protected": false, "required": true, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2488872504", "listRule": null, "viewRule": null, "createRule": null, "updateRule": null, "deleteRule": null, "name": "replicate_webhooks", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text4055988317", "max": 0, "min": 0, "name": "replicate_id", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "json2862543216", "maxSize": 5242880, "name": "jsondata", "presentable": false, "required": true, "system": false, "type": "json"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": ["CREATE INDEX `idx_p552cC1e1f` ON `replicate_webhooks` (`replicate_id`)"], "system": false}]