import React, {useEffect, useState} from 'react';
import {Navigate} from 'react-router-dom';
import {Card, CardContent, CardHeader, CardTitle} from '@/components/ui/card';
import {Button} from '@/components/ui/button';
import {Sparkles} from 'lucide-react';
import SharedLayout from '@/components/SharedLayout';
import {usePBContext} from "@/context/PocketbaseContext.tsx";
import {useTranslation} from 'react-i18next';
import {safeYMGoal} from "@/lib/utils.ts";
import * as VKID from '@vkid/sdk';
import {OAuthName} from '@vkid/sdk';

const codeVerifier = Array.from(crypto.getRandomValues(new Uint8Array(32)))
.map(byte => byte.toString(16).padStart(2, '0'))
.join('')
.replace(/[^a-zA-Z0-9]/g, '');


const redirectUrl = `${window.location.origin}/api/vkid/`

const Login = () => {
  const { user, pb, setUser } = usePBContext();
  const { t } = useTranslation();

  if (user) {
    return <Navigate to="/studio" replace />;
  }

  const login = async (provider: string) => {
    // const providers = await pb.collection('users').listAuthMethods();
    // console.log(providers)
    const authData = await pb.collection('users').authWithOAuth2({ provider: provider });
    console.log('auth data: ', authData)
    setUser(authData.record as undefined as any);
  }

  // const [vkRendered, setVkRendered] = useState(false);
  // useEffect(() => {
  //   if (!vkRendered) {
  //     pb.collection('users').listAuthMethods().then((providers) => {
  //       console.log(providers)
  //       if (!providers.oauth2.providers.find(p => p.name === 'vkid')) {
  //         console.warn('VK not found in providers list')
  //       } else {
  //         VKID.Config.init({
  //           app:  53896350,
  //           redirectUrl: 'https://api.holstai.ru/api/oauth2-redirect',
  //           codeVerifier,
  //           responseMode: VKID.ConfigResponseMode.Callback,
  //           source: VKID.ConfigSource.LOWCODE,
  //           scope: 'email',
  //         });
  //
  //         const oneTap = new VKID.OneTap();
  //
  //         const container = document.getElementById('vk-button');
  //
  //         if (container) {
  //           oneTap
  //           .render({
  //             container,
  //             showAlternativeLogin: true,
  //             oauthList: [
  //               OAuthName.MAIL,
  //               OAuthName.OK,
  //               OAuthName.VK
  //             ],
  //           })
  //           .on(VKID.WidgetEvents.LOAD, () => {
  //             safeYMGoal('login_attempt_vk');
  //           })
  //           .on(VKID.WidgetEvents.ERROR, console.error)
  //           .on(VKID.OneTapInternalEvents.LOGIN_SUCCESS, async function (payload) {
  //             try {
  //               console.log("Auth success", payload)
  //               console.log("loading...")
  //
  //               const authData = await pb.collection('users').authWithOAuth2Code(
  //                 'vkid',                         // provider:string
  //                 JSON.stringify(payload),        // code:string
  //                 codeVerifier,                   // codeVerifier:string
  //                 'https://api.holstai.ru/api/oauth2-redirect'   // redirectUrl:string
  //                 // createData: { [key: string]: any },
  //                 // options: RecordOptions
  //               )
  //               console.log('auth data: ', authData)
  //               setUser(authData.record as undefined as any);
  //             } catch (err) {
  //               console.error(err)
  //             }
  //           });
  //         }
  //
  //         setVkRendered(true);
  //       }
  //     })
  //   }
  // }, [pb, vkRendered]);
  return (
    <SharedLayout>
      <div className="min-h-[calc(100vh-200px)] flex items-center justify-center">
        <Card className="w-full max-w-md mx-auto shadow-lg border border-gray-200">
          <CardHeader className="text-center space-y-4">
            <div className="flex justify-center">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                <Sparkles className="w-7 h-7 text-white" />
              </div>
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900">{t('login.title')}</CardTitle>
            <p className="text-gray-600">{t('login.subtitle')}</p>
          </CardHeader>
          <CardContent className="space-y-6">
            <Button
              onClick={() => {
                safeYMGoal('login_attempt_google');
                return login('google');
              }}
              className="w-full h-12 bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 shadow-sm"
              variant="outline"
              size="lg"
            >
              <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24">
                <path
                  fill="#4285F4"
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                />
                <path
                  fill="#34A853"
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                />
                <path
                  fill="#FBBC05"
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                />
                <path
                  fill="#EA4335"
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                />
              </svg>
              {t('login.continueWithGoogle')}
            </Button>
            <Button
              onClick={() => {
                safeYMGoal('login_attempt_yandex');
                return login('yandex');
              }}
              className="w-full h-12 bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 shadow-sm"
              variant="outline"
              size="lg"
            >
              <img src="/yandex-logo-oauth.svg" alt="Yandex" className="w-5 h-5 mr-3" />
              {t('login.continueWithYandex')}
            </Button>
            {/*<Button*/}
            {/*  id='vk-button'*/}
            {/*  className="w-full h-48 bg-white text-gray-700 transition-all duration-200"*/}
            {/*  variant="ghost"*/}
            {/*  size="lg"*/}
            {/*>*/}
            {/*  /!*<img src="/vk-logo-oauth.svg" alt="VK" className="w-5 h-5 mr-3" />*!/*/}
            {/*  /!*{t('login.continueWithVK')}*!/*/}
            {/*</Button>*/}
          </CardContent>
        </Card>
      </div>
    </SharedLayout>
  );
};

export default Login;
