import Header from '@/components/landing/Header';
import Hero from '@/components/landing/Hero';
import Features from '@/components/landing/Features';
import FAQ from '@/components/landing/FAQ';
import Footer from '@/components/landing/Footer';
import { useReferral } from "@/hooks/use-referral.tsx";
import { useTranslation } from "react-i18next";
import { Helmet } from 'react-helmet-async';

const features = [
    {
        title: 'Генерация визуалов под тренды соцсетей',
        description: 'ИИ анализирует популярные мемы, челленджи и визуальные стили, а затем создаёт изображения или короткие видео в этом формате, адаптированные под ваш бренд. Вы получаете готовый к публикации контент, который «в тему» уже сегодня',
        image: 'smm/1.webp',
        reverse: false
    },
    {
        title: 'Мгновенная стилизация под бренд',
        description: 'Вы загружаете логотип, брендовые цвета и пару примеров контента — ИИ автоматически создаёт картинки и видео, которые выглядят так, будто их сделал ваш дизайнер. Все визуалы сразу в фирменном стиле, без ручной правки.',
        image: 'smm/2.webp',
        reverse: true
    },
    {
        title: 'Создание «вирусных» коротких видео',
        description: 'Опишите сценарий или эмоцию — и ИИ соберёт из образов и анимаций эффектный видеоролик длиной до 15 секунд, идеально подходящий для Reels или TikTok',
        image: 'smm/3.webp',
        reverse: false
    },
    {
        title: 'Персонализация визуалов под аудиторию',
        description: 'Для одной и той же идеи ИИ создаст несколько вариаций в разных стилях, цветах и эмоциях, чтобы тестировать, что лучше заходит у вашей ЦА.',
        image: 'smm/4.webp',
        reverse: true
    },
    {
        title: 'Генерация серий изображений для сторис',
        description: 'ИИ создаёт целую подборку картинок в едином стиле, которые можно выложить серией в Stories, создавая мини-историю или визуальный нарратив.',
        image: 'smm/5.webp',
        reverse: false
    }
]

const LandingSMM = () => {
    const _ = useReferral();
    const { t } = useTranslation();
    return (
        <div className="min-h-screen bg-background-primary">
            <Helmet>
                <title>{t("seo.smm.title")}</title>
                <meta name="description" content={t("seo.smm.description")} />
                <meta name="keywords" content={t("seo.smm.keywords")} />
                <meta property="og:title" content={t("seo.smm.title")} />
                <meta property="og:description" content={t("seo.smm.description")} />
                <meta property="og:type" content="website" />
                <meta name="twitter:card" content="summary_large_image" />
                <meta name="twitter:title" content={t("seo.smm.title")} />
                <meta name="twitter:description" content={t("seo.smm.description")} />
            </Helmet>
            <Header />
            <Hero title={t("landings.smm.title")} titleHighlight={t("landings.smm.titleHighlight")} subtitle={t("landings.smm.subtitle")} secondaryImage='/heroImages/smm/landingHero2.png' topSecondaryImage='/heroImages/smm/landingHero3.png' bottomSecondaryImage='/heroImages/smm/landingHero4.png'/>
            <Features features={features}/>
            <FAQ />
            <Footer />
        </div>
    );
};

export default LandingSMM;
