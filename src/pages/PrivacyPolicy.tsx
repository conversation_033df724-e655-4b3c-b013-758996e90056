import React from 'react';
import { useTranslation } from 'react-i18next';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const PrivacyPolicy = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <div className="mb-8">
          <Button
            variant="ghost"
            onClick={handleGoBack}
            className="mb-4 text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            {t('common.back')}
          </Button>
          <h1 className="text-4xl font-bold text-gray-900 mb-3">
            {t('privacyPolicy.title')}
          </h1>
          <p className="text-gray-600 text-lg">
            {t('privacyPolicy.lastUpdated')}
          </p>
        </div>

        {/* Content */}
        <Card className="border border-gray-200 shadow-sm">
          <CardContent className="p-8">
            <div className="prose prose-gray max-w-none">
              <p className="text-gray-700 leading-relaxed text-lg whitespace-pre-line">
                {t('privacyPolicy.content')}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="mt-12 pt-8 border-t border-gray-200 text-center">
          <Button
            onClick={handleGoBack}
            className="bg-[var(--primary-blue)] hover:bg-[var(--primary-blue-hover)] text-white px-6 py-2 rounded-full font-semibold"
          >
            {t('common.back')}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PrivacyPolicy;
