import * as React from "react"
import {useSearchParams} from "react-router-dom";
import {useCookies} from "react-cookie";

export function useReferral() {

  const [searchParams, setSearchParams] = useSearchParams();

  const [cookies, setCookie] = useCookies(['referral']);

  React.useEffect(() => {
    const referral = searchParams.get('referral');
    if (referral) {
      localStorage.setItem('referral', referral);
      setCookie('referral', referral, {path: '/'});
      console.log("Saved referral id ", referral)
      // Remove the query parameter from URL without triggering navigation
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.delete('referral');
      setSearchParams(newSearchParams, {replace: true});
    }
  }, [searchParams]);
  return cookies.referral
}

