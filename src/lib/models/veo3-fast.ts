import { t } from 'i18next';
import { ModelInputConfig, CommonParameters, CommonGroups } from './types';

export const veo3FastConfig: ModelInputConfig = {
  modelId: 'veo3-fast',
  modelName: 'Veo 3 Fast',
  description: 'A faster and cheaper version of Google\'s Veo 3 video model, with audio generation capabilities',

  parameters: {
    // Prompt enhancement
    enhance_prompt: {
      ...CommonParameters.enhancePrompt,
      description: 'Use Gemini to enhance your prompts for better video generation',
      order: 1,
      group: 'advanced'
    },

    // Negative prompt
    negative_prompt: {
      ...CommonParameters.negativePrompt,
      description: 'Description of what to discourage in the generated video',
      order: 2,
      group: 'advanced'
    },

    // Resolution - specific to veo3-fast
    resolution: {
      type: 'select',
      label: 'Resolution',
      description: 'Resolution of the generated video',
      defaultValue: '720p',
      options: [
        { value: '480p', label: '480p (854×480)' },
        { value: '720p', label: '720p (1280×720)' },
        { value: '1080p', label: '1080p (1920×1080)' }
      ],
      order: 3,
      group: 'advanced'
    },

    // Seed for reproducibility
    seed: {
      ...CommonParameters.seed,
      description: 'Random seed. Omit for random generations',
      order: 4,
      group: 'advanced'
    }
  },

  groups: {
    advanced: {
      ...CommonGroups.advanced,
      label: t('videoGenerator.settings.advancedSettings')
    }
  },

  metadata: {
    type: 'video',
    maxOutputs: 1,
    supportedFormats: ['mp4'],
    maxResolution: '1080p',
    estimatedTime: '30 seconds - 1 minute'
  }
};
