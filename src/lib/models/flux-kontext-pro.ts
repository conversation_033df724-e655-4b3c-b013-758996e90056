import { ModelInputConfig, CommonParameters, CommonGroups } from './types';
import {t} from "i18next";

export const fluxKontextProConfig: ModelInputConfig = {
  modelId: 'flux-kontext-pro',
  modelName: t('videoGenerator.imageSpecific.fluxKontextProConfig.modelName'),
  description: t('videoGenerator.imageSpecific.fluxKontextProConfig.description'),

  parameters: {
    // Input image for editing
    input_image: {
      type: 'file',
      label: t('videoGenerator.imageSpecific.fluxKontextProConfig.parameters.input_image.label'),
      description: t('videoGenerator.imageSpecific.fluxKontextProConfig.parameters.input_image.description'),
      accept: 'image/*',
      order: 1,
      group: 'advanced'
    },

    // Aspect ratio with Kontext-specific options
    aspect_ratio: {
      type: 'select',
      label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.label'),
      description: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.description'),
      defaultValue: 'match_input_image',
      options: [
        { value: 'match_input_image', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.match_input_image') },
        { value: '1:1', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.1:1') },
        { value: '16:9', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.16:9') },
        { value: '9:16', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.9:16') },
        { value: '4:3', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.4:3') },
        { value: '3:4', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.3:4') },
        { value: '3:2', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.3:2') },
        { value: '2:3', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.2:3') },
        { value: '4:5', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.4:5') },
        { value: '5:4', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.5:4') },
        { value: '21:9', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.21:9') },
        { value: '9:21', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.9:21') },
        { value: '2:1', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.2:1') },
        { value: '1:2', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.1:2') }
      ],
      order: 2,
      group: 'advanced'
    },

    // Safety tolerance (unique to Kontext Pro)
    safety_tolerance: {
      type: 'slider',
      label: t('videoGenerator.imageSpecific.fluxKontextProConfig.parameters.safety_tolerance.label'),
      description: t('videoGenerator.imageSpecific.fluxKontextProConfig.parameters.safety_tolerance.description'),
      defaultValue: 2,
      min: 0,
      max: 6,
      step: 1,
      order: 3,
      group: 'advanced'
    },

    // Output format
    output_format: {
      type: 'select',
      label: t('videoGenerator.imageSpecific.common.parameters.output_format.label'),
      description: t('videoGenerator.imageSpecific.common.parameters.output_format.description'),
      defaultValue: 'png',
      options: [
        { value: 'jpg', label: t('videoGenerator.imageSpecific.common.parameters.output_format.options.jpg') },
        { value: 'png', label: t('videoGenerator.imageSpecific.common.parameters.output_format.options.png') }
      ],
      order: 4,
      group: 'advanced'
    },

    // Seed for reproducibility
    seed: {
      ...CommonParameters.seed,
      description: t('videoGenerator.imageSpecific.common.parameters.seed.description'),
      order: 5,
      group: 'advanced'
    }
  },

  groups: {
    advanced: {
      ...CommonGroups.advanced,
      label: t('videoGenerator.settings.advancedSettings')
    }
  },

  metadata: {
    type: 'image',
    maxOutputs: 1,
    supportedFormats: ['jpg', 'png'],
    maxResolution: '2K',
    estimatedTime: '10-30 seconds'
  }
};