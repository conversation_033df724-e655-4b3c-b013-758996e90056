import { t } from 'i18next';
import { ModelInputConfig, CommonParameters, CommonGroups } from './types';

export const qwenImageConfig: ModelInputConfig = {
  modelId: 'qwen-image',
  modelName: t('videoGenerator.imageSpecific.qwenImageConfig.modelName'),
  description: t('videoGenerator.imageSpecific.qwenImageConfig.description'),

  parameters: {
    // Aspect ratio
    aspect_ratio: {
      type: 'select',
      label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.label'),
      description: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.description'),
      defaultValue: '',
      options: [
        { value: '1:1', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.1:1') },
        { value: '3:4', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.3:4') },
        { value: '4:3', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.4:3') },
        { value: '16:9', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.16:9') },
        { value: '9:16', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.9:16') },
        { value: '2:3', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.2:3') },
        { value: '3:2', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.3:2') }
      ],
      order: 1,
      group: 'advanced'
    },

    // Image size
    image_size: {
      type: 'select',
      label: t('videoGenerator.imageSpecific.qwenImageConfig.parameters.image_size.label'),
      description: t('videoGenerator.imageSpecific.qwenImageConfig.parameters.image_size.description'),
      defaultValue: '',
      placeholder: t('videoGenerator.parameters.placeholders.image_size'),
      options: [
        { value: 'square_hd', label: t('videoGenerator.imageSpecific.qwenImageConfig.parameters.image_size.options.square_hd') },
        { value: 'square', label: t('videoGenerator.imageSpecific.qwenImageConfig.parameters.image_size.options.square') },
        { value: 'portrait_4_3', label: t('videoGenerator.imageSpecific.qwenImageConfig.parameters.image_size.options.portrait_4_3') },
        { value: 'portrait_16_9', label: t('videoGenerator.imageSpecific.qwenImageConfig.parameters.image_size.options.portrait_16_9') },
        { value: 'landscape_4_3', label: t('videoGenerator.imageSpecific.qwenImageConfig.parameters.image_size.options.landscape_4_3') },
        { value: 'landscape_16_9', label: t('videoGenerator.imageSpecific.qwenImageConfig.parameters.image_size.options.landscape_16_9') }
      ],
      order: 2,
      group: 'advanced'
    },

    // Enhanced prompt
    enhance_prompt: {
      type: 'boolean',
      label: t('videoGenerator.parameters.labels.enhance_prompt'),
      description: t('videoGenerator.parameters.descriptions.enhance_prompt'),
      defaultValue: false,
      order: 3,
      group: 'advanced'
    },

    // Negative prompt
    negative_prompt: {
      type: 'textarea',
      label: t('videoGenerator.parameters.labels.negative_prompt'),
      description: t('videoGenerator.parameters.descriptions.negative_prompt'),
      defaultValue: '',
      placeholder: t('videoGenerator.parameters.placeholders.negative_prompt'),
      order: 4,
      group: 'advanced'
    },

    // Go fast
    go_fast: {
      type: 'boolean',
      label: t('videoGenerator.parameters.labels.go_fast'),
      description: t('videoGenerator.parameters.descriptions.go_fast'),
      defaultValue: false,
      order: 5,
      group: 'advanced'
    },

    // Number of inference steps
    num_inference_steps: {
      type: 'slider',
      label: t('videoGenerator.parameters.labels.num_inference_steps'),
      description: t('videoGenerator.parameters.descriptions.num_inference_steps'),
      defaultValue: 28,
      min: 1,
      max: 50,
      step: 1,
      order: 6,
      group: 'advanced'
    },

    // Guidance
    guidance: {
      type: 'slider',
      label: t('videoGenerator.parameters.labels.guidance'),
      description: t('videoGenerator.parameters.descriptions.guidance'),
      defaultValue: 3.0,
      min: 1.0,
      max: 10.0,
      step: 0.5,
      order: 7,
      group: 'advanced'
    },

    // Seed for reproducibility
    seed: {
      ...CommonParameters.seed,
      description: t('videoGenerator.imageSpecific.common.parameters.seed.description'),
      order: 8,
      group: 'advanced'
    },

    // Output format
    output_format: {
      type: 'select',
      label: t('videoGenerator.parameters.labels.output_format'),
      description: t('videoGenerator.parameters.descriptions.output_format'),
      defaultValue: 'webp',
      options: [
        { value: 'webp', label: t('videoGenerator.options.formats.webp') },
        { value: 'jpg', label: t('videoGenerator.options.formats.jpg') },
        { value: 'png', label: t('videoGenerator.options.formats.png') }
      ],
      order: 9,
      group: 'advanced'
    },

    // Output quality
    output_quality: {
      type: 'slider',
      label: t('videoGenerator.parameters.labels.output_quality'),
      description: t('videoGenerator.parameters.descriptions.output_quality'),
      defaultValue: 80,
      min: 0,
      max: 100,
      step: 10,
      order: 10,
      group: 'advanced'
    },

    // Disable safety checker
    disable_safety_checker: {
      type: 'boolean',
      label: t('videoGenerator.parameters.labels.disable_safety_checker'),
      description: t('videoGenerator.parameters.descriptions.disable_safety_checker'),
      defaultValue: false,
      order: 11,
      group: 'advanced'
    },

    // LoRA weights
    lora_weights: {
      type: 'text',
      label: t('videoGenerator.imageSpecific.qwenImageConfig.parameters.lora_weights.label'),
      description: t('videoGenerator.imageSpecific.qwenImageConfig.parameters.lora_weights.description'),
      defaultValue: '',
      placeholder: t('videoGenerator.imageSpecific.qwenImageConfig.parameters.lora_weights.placeholder'),
      order: 12,
      group: 'lora'
    },

    // LoRA scale
    lora_scale: {
      type: 'slider',
      label: t('videoGenerator.imageSpecific.qwenImageConfig.parameters.lora_scale.label'),
      description: t('videoGenerator.imageSpecific.qwenImageConfig.parameters.lora_scale.description'),
      defaultValue: 0.8,
      min: 0,
      max: 2,
      step: 0.1,
      order: 13,
      group: 'lora'
    }
  },

  groups: {
    advanced: {
      ...CommonGroups.advanced,
      label: t('videoGenerator.settings.advancedSettings')
    },
    lora: {
      name: 'lora',
      label: t('videoGenerator.imageSpecific.qwenImageConfig.groups.lora.label'),
      description: t('videoGenerator.imageSpecific.qwenImageConfig.groups.lora.description'),
      order: 2,
      collapsible: true,
      defaultExpanded: false
    }
  },

  metadata: {
    type: 'image',
    maxOutputs: 1,
    supportedFormats: ['webp', 'jpg', 'png'],
    maxResolution: '2K',
    estimatedTime: '5-30 seconds'
  }
};
