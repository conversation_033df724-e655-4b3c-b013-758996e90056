import { t } from 'i18next';
import { ModelInputConfig, CommonParameters, CommonGroups } from './types';

export const seedream3Config: ModelInputConfig = {
  modelId: 'seedream-3',
  modelName: t('videoGenerator.imageSpecific.seedream3Config.modelName'),
  description: t('videoGenerator.imageSpecific.seedream3Config.description'),

  parameters: {
    // Aspect ratio with Seedream-3 specific options
    aspect_ratio: {
      type: 'select',
      label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.label'),
      description: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.description'),
      defaultValue: '16:9',
      options: [
        { value: '1:1', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.1:1') },
        { value: '3:4', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.3:4') },
        { value: '4:3', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.4:3') },
        { value: '16:9', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.16:9') },
        { value: '9:16', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.9:16') },
        { value: '2:3', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.2:3') },
        { value: '3:2', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.3:2') },
        { value: '21:9', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.21:9') },
        { value: 'custom', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.custom') }
      ],
      order: 1,
      group: 'advanced'
    },

    // Size setting (unique to Seedream-3)
    size: {
      type: 'select',
      label: t('videoGenerator.imageSpecific.seedream3Config.parameters.size.label'),
      description: t('videoGenerator.imageSpecific.seedream3Config.parameters.size.description'),
      defaultValue: 'regular',
      options: [
        { value: 'small', label: t('videoGenerator.imageSpecific.seedream3Config.parameters.size.options.small') },
        { value: 'regular', label: t('videoGenerator.imageSpecific.seedream3Config.parameters.size.options.regular') },
        { value: 'big', label: t('videoGenerator.imageSpecific.seedream3Config.parameters.size.options.big') }
      ],
      order: 2,
      group: 'advanced',
      showWhen: {
        field: 'aspect_ratio',
        value: '!custom' // Show when aspect_ratio is NOT custom
      }
    },

    // Custom width (only when aspect_ratio is custom)
    width: {
      type: 'number',
      label: t('videoGenerator.imageSpecific.seedream3Config.parameters.width.label'),
      description: t('videoGenerator.imageSpecific.seedream3Config.parameters.width.description'),
      defaultValue: 2048,
      min: 512,
      max: 2048,
      step: 64,
      order: 3,
      group: 'advanced',
      showWhen: {
        field: 'aspect_ratio',
        value: 'custom'
      }
    },

    // Custom height (only when aspect_ratio is custom)
    height: {
      type: 'number',
      label: t('videoGenerator.imageSpecific.seedream3Config.parameters.height.label'),
      description: t('videoGenerator.imageSpecific.seedream3Config.parameters.height.description'),
      defaultValue: 2048,
      min: 512,
      max: 2048,
      step: 64,
      order: 4,
      group: 'advanced',
      showWhen: {
        field: 'aspect_ratio',
        value: 'custom'
      }
    },

    // Guidance scale
    guidance_scale: {
      type: 'slider',
      label: t('videoGenerator.imageSpecific.seedream3Config.parameters.guidance_scale.label'),
      description: t('videoGenerator.imageSpecific.seedream3Config.parameters.guidance_scale.description'),
      defaultValue: 2.5,
      min: 1,
      max: 10,
      step: 0.5,
      order: 5,
      group: 'advanced'
    },

    // Seed for reproducibility
    seed: {
      ...CommonParameters.seed,
      description: t('videoGenerator.imageSpecific.common.parameters.seed.description'),
      order: 6,
      group: 'advanced'
    }
  },

  groups: {
    advanced: {
      ...CommonGroups.advanced,
      label: t('videoGenerator.settings.advancedSettings')
    }
  },

  metadata: {
    type: 'image',
    maxOutputs: 1,
    supportedFormats: ['jpg'],
    maxResolution: '2K',
    estimatedTime: '5-15 seconds'
  }
};