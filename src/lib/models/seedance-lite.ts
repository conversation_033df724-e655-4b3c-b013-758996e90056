import { t } from 'i18next';
import { ModelInputConfig, CommonParameters, CommonGroups } from './types';

export const seedanceLiteConfig: ModelInputConfig = {
  modelId: 'seedance-lite',
  modelName: 'Seedance Lite',
  description: 'A video generation model that offers text-to-video and image-to-video support for 5s or 10s videos, at 480p and 720p resolution',

  parameters: {
    // Input image for image-to-video
    image: {
      type: 'file',
      label: 'Input Image (Optional)',
      description: 'Input image for image-to-video generation',
      accept: 'image/*',
      order: 1,
      group: 'advanced'
    },

    // Duration
    duration: {
      type: 'select',
      label: 'Duration',
      description: 'Video duration in seconds',
      defaultValue: 5,
      options: [
        { value: 5, label: '5 seconds' },
        { value: 10, label: '10 seconds' }
      ],
      order: 2,
      group: 'advanced'
    },

    // Resolution
    resolution: {
      type: 'select',
      label: 'Resolution',
      description: 'Video resolution',
      defaultValue: '720p',
      options: [
        { value: '480p', label: '480p (Faster)' },
        { value: '720p', label: '720p (Standard)' }
      ],
      order: 3,
      group: 'advanced'
    },

    // Aspect ratio (ignored if image is used)
    aspect_ratio: {
      type: 'select',
      label: 'Aspect Ratio',
      description: 'Video aspect ratio. Ignored if an image is used.',
      defaultValue: '16:9',
      options: [
        { value: '16:9', label: '16:9 (Landscape)' },
        { value: '4:3', label: '4:3 (Standard)' },
        { value: '1:1', label: '1:1 (Square)' },
        { value: '3:4', label: '3:4 (Portrait)' },
        { value: '9:16', label: '9:16 (Mobile Portrait)' },
        { value: '21:9', label: '21:9 (Ultrawide)' },
        { value: '9:21', label: '9:21 (Tall Portrait)' }
      ],
      order: 4,
      group: 'advanced',
      showWhen: {
        field: 'image',
        value: false // Show when no image is uploaded
      }
    },

    // Frame rate (fixed at 24)
    fps: {
      type: 'number',
      label: 'FPS',
      description: 'Frame rate (frames per second)',
      defaultValue: 24,
      min: 24,
      max: 24,
      order: 5,
      group: 'advanced'
    },

    // Camera settings
    camera_fixed: {
      type: 'boolean',
      label: 'Fixed Camera',
      description: 'Whether to fix camera position',
      defaultValue: false,
      order: 6,
      group: 'advanced'
    },

    // Seed for reproducibility
    seed: {
      ...CommonParameters.seed,
      description: 'Random seed. Set for reproducible generation',
      order: 7,
      group: 'advanced'
    }
  },

  groups: {
    advanced: {
      ...CommonGroups.advanced,
      label: t('videoGenerator.settings.advancedSettings')
    }
  },

  metadata: {
    type: 'video',
    maxOutputs: 1,
    supportedFormats: ['mp4'],
    maxResolution: '720p',
    estimatedTime: '1-2 minutes'
  }
};
