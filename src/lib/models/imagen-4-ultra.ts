import { t } from 'i18next';
import { ModelInputConfig, CommonParameters, CommonGroups } from './types';

export const imagen4UltraConfig: ModelInputConfig = {
  modelId: 'imagen-4-ultra',
  modelName: t('videoGenerator.imageSpecific.imagen4UltraConfig.modelName'),
  description: t('videoGenerator.imageSpecific.imagen4UltraConfig.description'),

  parameters: {
    // Aspect ratio with Imagen-4-Ultra specific options
    aspect_ratio: {
      type: 'select',
      label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.label'),
      description: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.description'),
      defaultValue: '1:1',
      options: [
        { value: '1:1', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.1:1') },
        { value: '9:16', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.9:16') },
        { value: '16:9', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.16:9') },
        { value: '3:4', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.3:4') },
        { value: '4:3', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.4:3') }
      ],
      order: 1,
      group: 'advanced'
    },

    // Safety filter level (unique to Imagen-4-Ultra)
    safety_filter_level: {
      type: 'select',
      label: t('videoGenerator.imageSpecific.imagen4UltraConfig.parameters.safety_filter_level.label'),
      description: t('videoGenerator.imageSpecific.imagen4UltraConfig.parameters.safety_filter_level.description'),
      defaultValue: 'block_only_high',
      options: [
        {
          value: 'block_low_and_above',
          label: t('videoGenerator.imageSpecific.imagen4UltraConfig.parameters.safety_filter_level.options.block_low_and_above.label'),
          description: t('videoGenerator.imageSpecific.imagen4UltraConfig.parameters.safety_filter_level.options.block_low_and_above.description')
        },
        {
          value: 'block_medium_and_above',
          label: t('videoGenerator.imageSpecific.imagen4UltraConfig.parameters.safety_filter_level.options.block_medium_and_above.label'),
          description: t('videoGenerator.imageSpecific.imagen4UltraConfig.parameters.safety_filter_level.options.block_medium_and_above.description')
        },
        {
          value: 'block_only_high',
          label: t('videoGenerator.imageSpecific.imagen4UltraConfig.parameters.safety_filter_level.options.block_only_high.label'),
          description: t('videoGenerator.imageSpecific.imagen4UltraConfig.parameters.safety_filter_level.options.block_only_high.description')
        }
      ],
      order: 2,
      group: 'advanced'
    },

    // Output format
    output_format: {
      type: 'select',
      label: t('videoGenerator.imageSpecific.common.parameters.output_format.label'),
      description: t('videoGenerator.imageSpecific.common.parameters.output_format.description'),
      defaultValue: 'jpg',
      options: [
        { value: 'jpg', label: t('videoGenerator.imageSpecific.common.parameters.output_format.options.jpg') },
        { value: 'png', label: t('videoGenerator.imageSpecific.common.parameters.output_format.options.png') }
      ],
      order: 3,
      group: 'advanced'
    }
  },

  groups: {
    advanced: {
      ...CommonGroups.advanced,
      label: t('videoGenerator.settings.advancedSettings')
    }
  },

  metadata: {
    type: 'image',
    maxOutputs: 1,
    supportedFormats: ['jpg', 'png'],
    maxResolution: '2K',
    estimatedTime: '15-45 seconds'
  }
};