import { t } from 'i18next';
import { ModelInputConfig, CommonParameters, CommonGroups } from './types';

export const veo3Config: ModelInputConfig = {
  modelId: 'veo3',
  modelName: 'Veo 3',
  description: 'Google\'s flagship Veo 3 text to video model, with audio generation capabilities',

  parameters: {
    // Prompt enhancement
    enhance_prompt: {
      ...CommonParameters.enhancePrompt,
      description: 'Use Gemini to enhance your prompts for better video generation',
      order: 1,
      group: 'advanced'
    },

    // Negative prompt
    negative_prompt: {
      ...CommonParameters.negativePrompt,
      description: 'Description of what to discourage in the generated video',
      order: 2,
      group: 'advanced'
    },

    // Seed for reproducibility
    seed: {
      ...CommonParameters.seed,
      description: 'Random seed. Omit for random generations',
      order: 3,
      group: 'advanced'
    }
  },

  groups: {
    advanced: {
      ...CommonGroups.advanced,
      label: t('videoGenerator.settings.advancedSettings')
    }
  },

  metadata: {
    type: 'video',
    maxOutputs: 1,
    supportedFormats: ['mp4'],
    maxResolution: '4K',
    estimatedTime: '1-3 minutes'
  }
};
