import { ModelInputConfig, CommonParameters, CommonGroups } from './types';
import {t} from "i18next";

export const fluxDevConfig: ModelInputConfig = {
  modelId: 'flux-dev',
  modelName: t('videoGenerator.imageSpecific.fluxDevConfig.modelName'),
  description: t('videoGenerator.imageSpecific.fluxDevConfig.description'),

  parameters: {
    // Aspect ratio with FLUX-specific options
    aspect_ratio: {
      type: 'select',
      label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.label'),
      description: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.description'),
      defaultValue: '1:1',
      options: [
        { value: '1:1', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.1:1') },
        { value: '16:9', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.16:9') },
        { value: '21:9', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.21:9') },
        { value: '3:2', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.3:2') },
        { value: '2:3', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.2:3') },
        { value: '4:5', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.4:5') },
        { value: '5:4', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.5:4') },
        { value: '3:4', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.3:4') },
        { value: '4:3', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.4:3') },
        { value: '9:16', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.9:16') },
        { value: '9:21', label: t('videoGenerator.imageSpecific.common.parameters.aspect_ratio.options.9:21') }
      ],
      order: 1,
      group: 'advanced'
    },

    // Image input for img2img mode
    image: {
      type: 'file',
      label: t('videoGenerator.imageSpecific.fluxDevConfig.parameters.image.label'),
      description: t('videoGenerator.imageSpecific.fluxDevConfig.parameters.image.description'),
      accept: 'image/*',
      order: 2,
      group: 'advanced'
    },

    // Prompt strength for img2img
    prompt_strength: {
      type: 'slider',
      label: t('videoGenerator.imageSpecific.fluxDevConfig.parameters.prompt_strength.label'),
      description: t('videoGenerator.imageSpecific.fluxDevConfig.parameters.prompt_strength.description'),
      defaultValue: 0.8,
      min: 0,
      max: 1,
      step: 0.1,
      order: 3,
      group: 'advanced',
      showWhen: {
        field: 'image',
        value: true // Show when image is uploaded
      }
    },

    // Number of outputs
    num_outputs: {
      type: 'select',
      label: t('videoGenerator.imageSpecific.fluxDevConfig.parameters.num_outputs.label'),
      description: t('videoGenerator.imageSpecific.fluxDevConfig.parameters.num_outputs.description'),
      defaultValue: 1,
      options: [
        { value: 1, label: t('videoGenerator.imageSpecific.fluxDevConfig.parameters.num_outputs.options.1') },
        { value: 2, label: t('videoGenerator.imageSpecific.fluxDevConfig.parameters.num_outputs.options.2') },
        { value: 4, label: t('videoGenerator.imageSpecific.fluxDevConfig.parameters.num_outputs.options.4') }
      ],
      order: 4,
      group: 'advanced'
    },

    // Quality and performance settings
    num_inference_steps: {
      type: 'slider',
      label: t('videoGenerator.imageSpecific.fluxDevConfig.parameters.num_inference_steps.label'),
      description: t('videoGenerator.imageSpecific.fluxDevConfig.parameters.num_inference_steps.description'),
      defaultValue: 28,
      min: 1,
      max: 50,
      step: 1,
      order: 5,
      group: 'advanced'
    },

    guidance: {
      type: 'slider',
      label: t('videoGenerator.imageSpecific.fluxDevConfig.parameters.guidance.label'),
      description: t('videoGenerator.imageSpecific.fluxDevConfig.parameters.guidance.description'),
      defaultValue: 3,
      min: 0,
      max: 10,
      step: 0.5,
      order: 6,
      group: 'advanced'
    },

    go_fast: {
      type: 'boolean',
      label: t('videoGenerator.imageSpecific.fluxDevConfig.parameters.go_fast.label'),
      description: t('videoGenerator.imageSpecific.fluxDevConfig.parameters.go_fast.description'),
      defaultValue: true,
      order: 7,
      group: 'advanced'
    },

    megapixels: {
      type: 'select',
      label: t('videoGenerator.imageSpecific.fluxDevConfig.parameters.megapixels.label'),
      description: t('videoGenerator.imageSpecific.fluxDevConfig.parameters.megapixels.description'),
      defaultValue: '1',
      options: [
        { value: '0.25', label: t('videoGenerator.imageSpecific.fluxDevConfig.parameters.megapixels.options.0.25') },
        { value: '1', label: t('videoGenerator.imageSpecific.fluxDevConfig.parameters.megapixels.options.1') }
      ],
      order: 8,
      group: 'advanced'
    },

    // Output settings
    output_format: {
      type: 'select',
      label: t('videoGenerator.imageSpecific.common.parameters.output_format.label'),
      description: t('videoGenerator.imageSpecific.common.parameters.output_format.description'),
      defaultValue: 'webp',
      options: [
        { value: 'webp', label: t('videoGenerator.imageSpecific.common.parameters.output_format.options.webp') },
        { value: 'jpg', label: t('videoGenerator.imageSpecific.common.parameters.output_format.options.jpg') },
        { value: 'png', label: t('videoGenerator.imageSpecific.common.parameters.output_format.options.png') }
      ],
      order: 9,
      group: 'advanced'
    },

    output_quality: {
      type: 'slider',
      label: t('videoGenerator.imageSpecific.fluxDevConfig.parameters.output_quality.label'),
      description: t('videoGenerator.imageSpecific.fluxDevConfig.parameters.output_quality.description'),
      defaultValue: 80,
      min: 0,
      max: 100,
      step: 5,
      order: 10,
      group: 'advanced'
    },

    disable_safety_checker: {
      type: 'boolean',
      label: t('videoGenerator.imageSpecific.fluxDevConfig.parameters.disable_safety_checker.label'),
      description: t('videoGenerator.imageSpecific.fluxDevConfig.parameters.disable_safety_checker.description'),
      defaultValue: false,
      order: 11,
      group: 'advanced'
    },

    // Seed for reproducibility
    seed: {
      ...CommonParameters.seed,
      description: t('videoGenerator.imageSpecific.common.parameters.seed.description'),
      order: 12,
      group: 'advanced'
    }
  },

  groups: {
    advanced: {
      ...CommonGroups.advanced,
      label: t('videoGenerator.settings.advancedSettings')
    }
  },

  metadata: {
    type: 'image',
    maxOutputs: 4,
    supportedFormats: ['webp', 'jpg', 'png'],
    maxResolution: '2K',
    estimatedTime: '10-30 seconds'
  }
};