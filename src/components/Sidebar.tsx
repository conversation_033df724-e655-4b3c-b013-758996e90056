
import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import {User, Sparkles, Plus, Clock, CreditCard, MessageCircleQuestion} from 'lucide-react';
import {usePBContext} from "@/context/PocketbaseContext.tsx";
import { useTranslation } from 'react-i18next';
import {useQuery} from "@tanstack/react-query";
import {api, queryKeys} from "@/lib/api.ts";
import { useIsMobile } from "@/hooks/use-mobile";
import { Sheet, SheetContent } from "@/components/ui/sheet";

interface SidebarProps {
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
}

const Sidebar = ({ isOpen = false, onOpenChange }: SidebarProps) => {
  const location = useLocation();
  const { user, pb } = usePBContext();
  const { t } = useTranslation();
  const isMobile = useIsMobile();

  const menuItems = [
    { icon: Plus, label: t('sidebar.navigation.create'), path: '/studio', active: true, external: false },
    { icon: Clock, label: t('sidebar.navigation.history'), path: '/history', active: true, external: false},
    { icon: CreditCard, label: t('sidebar.navigation.pricing'), path: '/pricing', active: true, external: false },
    { icon: User, label: t('sidebar.navigation.profile'), path: '/profile', active: true, external: false },
    { icon: MessageCircleQuestion, label: t('sidebar.navigation.support'), path: 'https://t.me/holstai_support_bot', active: false, external: true },
  ];

  const {data: userBalance } = useQuery({
      queryKey: queryKeys.userTokenBalance(user?.id || ''),
      queryFn: async () => {
          if (!user?.id) {
            throw new Error('No user ID found');
          }

          return await api.getUserTokenBalance(pb, user.id);
      },
      enabled: !!user?.id
  });

  // Sidebar content component
  const SidebarContent = () => (
    <div className="h-full flex flex-col bg-white">
      {/* Logo */}
      <div className="p-6 border-b border-gray-100 flex-shrink-0">
        <Link to="/" className="flex items-center space-x-3" onClick={() => onOpenChange?.(false)}>
          <img
            src="/ForLightBG.png"
            alt="Logo"
            className="w-8 h-8 object-contain"
          />
          <span className="text-xl font-bold text-gray-900">{t('sidebar.brand')}</span>
        </Link>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 overflow-hidden pt-4">
        <ul className="space-y-1">
          {menuItems.map((item, index) => {
            const isActive = location.pathname === item.path && item.active;
            return (
              <li key={index}>
                <Link
                  to={item.path}
                  {...item.external ? { target: '_blank', rel: 'noopener noreferrer' } : {}}
                  onClick={() => onOpenChange?.(false)}
                  className={`w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg transition-all duration-200 ${
                    isActive
                      ? 'bg-gray-100 text-gray-900 font-medium'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  <item.icon className="w-5 h-5" />
                  <span>{item.label}</span>
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* User Profile */}
      <div className="p-4 border-t border-gray-100 flex-shrink-0">
        <Link
          to="/profile"
          onClick={() => onOpenChange?.(false)}
          className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
        >
          {user && user.avatar ? (
            <img
              src={pb.files.getURL(user, user.avatar)}
              alt={t('sidebar.user.profileAlt')}
              className="w-8 h-8 rounded-full"
            />
          ) : (
            <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-blue-400 rounded-full flex items-center justify-center">
              <User className="w-4 h-4 text-white" />
            </div>
          )}
          <div className="flex-1">
            <p className="text-gray-900 font-medium text-sm">{user ? user.name : t('sidebar.user.defaultName')}</p>
            <p className="text-gray-500 text-xs">{userBalance} {t('sidebar.user.credits')}</p>
          </div>
        </Link>
      </div>
    </div>
  );

  // Mobile version using Sheet
  if (isMobile) {
    return (
      <Sheet open={isOpen} onOpenChange={onOpenChange}>
        <SheetContent
          side="left"
          className="w-80 p-0 bg-white border-r border-gray-200"
        >
          <SidebarContent />
        </SheetContent>
      </Sheet>
    );
  }

  // Desktop version
  return (
    <div className="w-64 bg-white h-screen flex flex-col border-r border-gray-200 overflow-hidden">
      <SidebarContent />
    </div>
  );
};

export default Sidebar;
