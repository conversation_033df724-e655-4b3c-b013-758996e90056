import React, {useEffect} from 'react';
import {Navigate, Outlet, useSearchParams} from 'react-router-dom';
import {usePBContext} from "@/context/PocketbaseContext.tsx";
import {useReferral} from "@/hooks/use-referral.tsx";

interface ProtectedRouteProps {
    children?: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
    const { user } = usePBContext();

    const isAuthenticated = user != null;

    if (!isAuthenticated) {
        return <Navigate to="/login" replace />;
    }

    // import for effects
    const _ = useReferral();

  return children ? <>{children}</> : <Outlet />;
};

export default ProtectedRoute;
