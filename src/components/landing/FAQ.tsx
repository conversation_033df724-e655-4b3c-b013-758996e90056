import { useState } from 'react';
import { ChevronDown } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const FAQ = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);
  const { t } = useTranslation();

  const faqs = [
    {
      question: t('faq.items.whatIsAi.question'),
      answer: t('faq.items.whatIsAi.answer')
    },
    {
      question: t('faq.items.howLong.question'),
      answer: t('faq.items.howLong.answer')
    },
    {
      question: t('faq.items.fileFormats.question'),
      answer: t('faq.items.fileFormats.answer')
    },
    {
      question: t('faq.items.commercial.question'),
      answer: t('faq.items.commercial.answer')
    },
    {
      question: t('faq.items.billing.question'),
      answer: t('faq.items.billing.answer')
    },
    {
      question: t('faq.items.dataPrivacy.question'),
      answer: t('faq.items.dataPrivacy.answer')
    },
    {
      question: t('faq.items.refunds.question'),
      answer: t('faq.items.refunds.answer')
    }
  ];

  return (
    <section id="faq" className="py-20 lg:py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-4 text-text-dark">
            {t('faq.title')}{' '}
            <span className="bg-gradient-to-r from-blue-800 to-blue-400 bg-clip-text text-transparent">{t('faq.titleHighlight')}</span>
          </h2>
          <p className="text-xl text-text-light max-w-3xl mx-auto">
            {t('faq.subtitle')}
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          {faqs.map((faq, index) => (
            <div
              key={index}
              className="card bg-background-primary rounded-xl mb-4 overflow-hidden animate-fade-in-up"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <button
                className="w-full px-8 py-4 text-left flex items-center justify-between hover:bg-background-secondary transition-colors duration-200"
                onClick={() => setOpenIndex(openIndex === index ? null : index)}
              >
                <h3 className="text-lg font-semibold text-text-dark pr-4">{faq.question}</h3>
                <ChevronDown
                  className={`w-6 h-6 text-text-light transition-transform duration-200 flex-shrink-0 ${
                    openIndex === index ? 'transform rotate-180' : ''
                  }`}
                />
              </button>

              {openIndex === index && (
                <div className="px-8 pb-4 pt-2">
                  <p className="text-text-light leading-relaxed">{faq.answer}</p>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FAQ;
