import { cn } from '@/lib/utils';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

interface ImageElement {
  type: string;
  subject?: string;
  position: string;
  content?: any;
  element?: string; // Add this line to fix the TypeScript error
}

interface FeatureShowcaseProps {
  title: string;
  description: string;
  image: {
    type: string;
    elements: ImageElement[];
  };
  reverse?: boolean;
  className?: string;
}

export function FeatureShowcase({
  title,
  description,
  image,
  reverse = false,
  className,
}: FeatureShowcaseProps) {
  const mainImage = image.elements.find(el => el.position === 'main');
  const navigate = useNavigate();
  const { t } = useTranslation();

  return (
    <div className={cn('w-full py-10 lg:py-10', className)}>
      <div
        className={cn(
          'w-full flex flex-col lg:flex-row items-center gap-8 lg:gap-12 rounded-2xl lg:rounded-2xl shadow-2xl border border-gray-200 px-8 py-8',
          reverse && 'lg:flex-row-reverse'
        )}
      >
        {/* Text Content */}
        <div
          className={cn(
            'w-full lg:w-1/2 space-y-6',
            // Remove padding if we want flush alignment
            !reverse ? 'lg:pr-0' : 'lg:pl-0'
          )}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-text-dark">
            {title}
          </h2>
          <p className="text-lg text-text-light leading-relaxed">
            {description}
          </p>
          {/* CTA Button - Only visible on desktop */}
          <div className="mb-8 hidden lg:block">
              <button
                onClick={() => navigate('/studio')}
                className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                {t('hero.startCreating')}
              </button>
            </div>
        </div>

        {/* Image */}
        <div
          className={cn(
            'aspect-square relative overflow-hidden w-full max-w-[400px]',
            !reverse ? 'lg:ml-auto' : 'lg:mr-auto'
          )}
        >
          <img
            src={`/samples/${mainImage?.subject}`}
            alt={mainImage?.subject || 'AI Generated Image'}
            className="w-full h-full object-contain"
          />
        </div>

        {/* CTA Button - Only visible on mobile, positioned after image */}
        <div className="w-full lg:hidden">
          <button
            onClick={() => navigate('/studio')}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
          >
            {t('hero.startCreating')}
          </button>
        </div>
      </div>
    </div>
  );
}