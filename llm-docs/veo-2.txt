## Basic model info

Model name: google/veo-2
Model description: State of the art video generation model. Veo 2 can faithfully follow simple and complex instructions, and convincingly simulates real-world physics as well as a wide range of visual styles.


## Model inputs

- prompt (optional): Text prompt for video generation (string)
- image (optional): Input image to start generating from. Ideal images are 16:9 or 9:16 and 1280x720 or 720x1280, depending on the aspect ratio you choose. (string)
- aspect_ratio (optional): Video aspect ratio (string)
- duration (optional): Video duration (integer)
- seed (optional): Random seed. Omit for random generations (integer)


## Input schema

{
  "type": "object",
  "title": "Input",
  "properties": {
    "seed": {
      "type": "integer",
      "title": "Seed",
      "x-order": 4,
      "description": "Random seed. Omit for random generations"
    },
    "image": {
      "type": "string",
      "title": "Image",
      "format": "uri",
      "x-order": 1,
      "nullable": true,
      "description": "Input image to start generating from. Ideal images are 16:9 or 9:16 and 1280x720 or 720x1280, depending on the aspect ratio you choose."
    },
    "prompt": {
      "type": "string",
      "title": "Prompt",
      "x-order": 0,
      "nullable": true,
      "description": "Text prompt for video generation"
    },
    "duration": {
      "enum": [
        5,
        6,
        7,
        8
      ],
      "type": "integer",
      "title": "duration",
      "description": "Video duration",
      "default": 5,
      "x-order": 3
    },
    "aspect_ratio": {
      "enum": [
        "16:9",
        "9:16"
      ],
      "type": "string",
      "title": "aspect_ratio",
      "description": "Video aspect ratio",
      "default": "16:9",
      "x-order": 2
    }
  }
}

## Model output schema

{
  "type": "string",
  "title": "Output",
  "format": "uri"
}

If the input or output schema includes a format of URI, it is referring to a file.


## Example inputs and outputs

Use these example outputs to better understand the types of inputs the model accepts, and the types of outputs the model returns:

### Example (https://replicate.com/p/tjqhsk4eddrma0cn7w38c91tq8)

#### Input

```json
{
  "prompt": "a dog riding a skateboard",
  "duration": 5,
  "aspect_ratio": "16:9",
  "enhance_prompt": true
}
```

#### Output

```json
"https://replicate.delivery/xezq/WB9h80FyAYKRINH8bcNP4lvvw0bwmhIJ0Olz8fP1YUaBTfSUA/tmpgbhdxrta.mp4"
```


## Model readme

> # Veo 2
>
> Veo 2 is a video generation model designed to create videos with realistic motion and high-quality output (up to 4K resolution). The model can be accessed via VideoFX and Vertex AI.
>
> ## Key Features
>
> ### Quality and Control
>
> - Follows both simple and complex instructions
> - Simulates real-world physics
> - Supports a wide range of visual styles
>
> ### Technical Capabilities
>
> - Enhanced realism and detail with reduced artifacts
> - Advanced motion representation with accurate physics simulation
> - Precise interpretation of camera instructions (shot styles, angles, movements)
>
> ## Benchmarks
>
> Veo 2 has been evaluated against other video generation models through human ratings on the MovieGenBench dataset (1003 prompts, released by Meta). Testing parameters:
> - 720p resolution for all comparisons
> - Veo samples: 8 seconds duration
> - VideoGen samples: 10 seconds duration
> - Other models: 5 seconds duration
>
> ## Known Limitations
>
> - Challenges with maintaining complete consistency throughout complex scenes
> - Difficulty with complex motion
> - Some limitations in creating highly realistic or intricate videos
>
> ## Development Credits
>
> Veo 2 was developed by a team at Google DeepMind. Key research and engineering contributions from:
>
> Agrim Gupta, Ali Razavi, Ankush Gupta, Dumitru Erhan, Eric Lau, Frank Belletti, Gabe Barth-Maron, Hakan Erdogan, Hakim Sidahmed, Henna Nandwani, Hernan Moraldo, Hyunjik Kim, Jeff Donahue, José Lezama, Kory Mathewson, Kurtis David, Marc van Zee, Medhini Narasimhan, Miaosen Wang, Mohammad Babaeizadeh, Nelly Papalampidi, Nick Pezzotti, Nilpa Jha, Parker Barnes, Pieter-Jan Kindermans, Rachel Hornung, Ruben Villegas, Ryan Poplin, Salah Zaiem, Sander Dieleman, Sayna Ebrahimi, Scott Wisdom, Serena Zhang, Shlomi Fruchter, Weizhe Hua, Xinchen Yan, Yuqing Du, Yutian Chen
>
> Additional acknowledgments to numerous partners across Google DeepMind and Google.
>
> ## Related Technologies
> - Imagen (text-to-image model)
> - Gemini (AI model)
> - SynthID (watermarking and identification for AI-generated images)

