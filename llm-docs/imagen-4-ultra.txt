## Basic model info

Model name: google/imagen-4-ultra
Model description: Use this ultra version of Imagen 4 when quality matters more than speed and cost


## Model inputs

- prompt (required): Text prompt for image generation (string)
- aspect_ratio (optional): Aspect ratio of the generated image (string)
- safety_filter_level (optional): block_low_and_above is strictest, block_medium_and_above blocks some prompts, block_only_high is most permissive but some prompts will still be blocked (string)
- output_format (optional): Format of the output image (string)


## Input schema
{
  "type": "object",
  "title": "Input",
  "required": [
    "prompt"
  ],
  "properties": {
    "prompt": {
      "type": "string",
      "title": "Prompt",
      "x-order": 0,
      "description": "Text prompt for image generation"
    },
    "aspect_ratio": {
      "enum": [
        "1:1",
        "9:16",
        "16:9",
        "3:4",
        "4:3"
      ],
      "type": "string",
      "title": "aspect_ratio",
      "description": "Aspect ratio of the generated image",
      "default": "1:1",
      "x-order": 1
    },
    "output_format": {
      "enum": [
        "jpg",
        "png"
      ],
      "type": "string",
      "title": "output_format",
      "description": "Format of the output image",
      "default": "jpg",
      "x-order": 3
    },
    "safety_filter_level": {
      "enum": [
        "block_low_and_above",
        "block_medium_and_above",
        "block_only_high"
      ],
      "type": "string",
      "title": "safety_filter_level",
      "description": "block_low_and_above is strictest, block_medium_and_above blocks some prompts, block_only_high is most permissive but some prompts will still be blocked",
      "default": "block_only_high",
      "x-order": 2
    }
  }
}

## Model output schema

{
  "type": "string",
  "title": "Output",
  "format": "uri"
}

If the input or output schema includes a format of URI, it is referring to a file.


## Example inputs and outputs

Use these example outputs to better understand the types of inputs the model accepts, and the types of outputs the model returns:

### Example (https://replicate.com/p/4g6nk23689rmc0cqceqbhd4g0g)

#### Input

```json
{
  "prompt": "The photo: Create a cinematic, photorealistic medium shot capturing the nostalgic warmth of a mid-2000s indie film. The focus is a young woman with a sleek, straight bob haircut in cool platinum white with freckled skin, looking directly and intently into the camera lens with a knowing smirk, her head is looking up slightly. She wears an oversized band t-shirt that says \"Imagen 4 Ultra on Replicate\" in huge stylized text over a long-sleeved striped top and simple silver stud earrings. The lighting is soft, golden hour sunlight creating lens flare and illuminating dust motes in the air. The background shows a blurred outdoor urban setting with graffiti-covered walls (the graffiti says \"ultra\" in stylized graffiti lettering), rendered with a shallow depth of field. Natural film grain, a warm, slightly muted color palette, and sharp focus on her expressive eyes enhance the intimate, authentic feel",
  "aspect_ratio": "16:9",
  "output_format": "jpg",
  "safety_filter_level": "block_only_high"
}
```

#### Output

```json
"https://replicate.delivery/xezq/eAsULzF8tzzXVSUtp7rvlDPqEkePBcLeTTWnqsSaCKYRtLspA/tmpikc6119g.jpg"
```


## Model readme

> ## Google’s Imagen 4 Ultra
>
> - [Imagen 4](https://replicate.com/google/imagen-4)
> - [Imagen 4 Ultra](https://replicate.com/google/imagen-4-ultra)
> - [Imagen 4 Fast](https://replicate.com/google/imagen-4-fast)
>
> High-quality image generation model featuring:
>
> - **Fine detail rendering**: Superior clarity for intricate elements like fabrics, water droplets, and animal fur
> - **Style versatility**: Excels in both photorealistic and abstract styles
> - **Resolution flexibility**: Creates images in various aspect ratios up to 2K resolution
> - **Typography improvements**: Significantly enhanced text rendering capabilities for greeting cards, posters, and comics
> - **Fast variant**: Upcoming version promises up to 10x faster generation compared to Imagen 3

