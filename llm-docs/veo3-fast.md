## Basic model info

Model name: google/veo-3-fast
Model description: A faster and cheaper version of Google’s Veo 3 video model, with audio


## Model inputs

- prompt (required): Text prompt for video generation (string)
- negative_prompt (optional): Description of what to discourage in the generated video (string)
- resolution (optional): Resolution of the generated video (string)
- seed (optional): Random seed. Omit for random generations (integer)


## Model output schema

{
  "type": "string",
  "title": "Output",
  "format": "uri"
}

If the input or output schema includes a format of URI, it is referring to a file.


## Example inputs and outputs

Use these example outputs to better understand the types of inputs the model accepts, and the types of outputs the model returns:

### Example (https://replicate.com/p/rvdw9f861xrme0cqy2g9a1c7bm)

#### Input

```json
{
  "prompt": "gorilla riding a moped through busy italian city",
  "enhance_prompt": true
}
```

#### Output

```json
"https://replicate.delivery/xezq/iY9PbAFJN2LJOBx1imI08Bhz4lJX1ZHBf5WakE1JGNtfKHfpA/tmp8ai_03z8.mp4"
```


## Model readme

> ## Veo 3 fast
> 
> A faster and cheaper version of Google’s flagship Veo 3 video model. Now you can generate videos with native audio quickly and cheaply.
> 
> Veo 3 fast still has state of the art quality and prompt following, and sometimes the generated audio outperforms native Veo 3.

