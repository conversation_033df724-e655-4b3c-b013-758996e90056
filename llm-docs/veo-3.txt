## Basic model info

Model name: google/veo-3
Model description: Sound on: Google’s flagship Veo 3 text to video model, with audio


## Model inputs

- prompt (required): Text prompt for video generation (string)
- enhance_prompt (optional): Use Gemini to enhance your prompts (boolean)
- negative_prompt (optional): Description of what to discourage in the generated video (string)
- seed (optional): Random seed. Omit for random generations (integer)

## Input schema

{
  "type": "object",
  "title": "Input",
  "required": [
    "prompt"
  ],
  "properties": {
    "seed": {
      "type": "integer",
      "title": "Seed",
      "x-order": 3,
      "description": "Random seed. Omit for random generations"
    },
    "prompt": {
      "type": "string",
      "title": "Prompt",
      "x-order": 0,
      "description": "Text prompt for video generation"
    },
    "enhance_prompt": {
      "type": "boolean",
      "title": "Enhance Prompt",
      "default": true,
      "x-order": 1,
      "description": "Use Gemini to enhance your prompts"
    },
    "negative_prompt": {
      "type": "string",
      "title": "Negative Prompt",
      "x-order": 2,
      "nullable": true,
      "description": "Description of what to discourage in the generated video"
    }
  }
}


## Model output schema

{
  "type": "string",
  "title": "Output",
  "format": "uri"
}

If the input or output schema includes a format of URI, it is referring to a file.


## Example inputs and outputs

Use these example outputs to better understand the types of inputs the model accepts, and the types of outputs the model returns:

### Example (https://replicate.com/p/fmba0q1379rmc0cpyeas42r6kr)

#### Input

```json
{
  "prompt": "gorilla riding a moped through busy italian city",
  "aspect_ratio": "16:9"
}
```

#### Output

```json
"https://replicate.delivery/xezq/xfz0U06bqSw1AyiJsw7LjOjVXOBpmjJfe7K9XtRLifW26q7SB/tmpi29yh_f0.mp4"
```


## Model readme

> ##Veo 3 - Google
>
> Veo3 is Google DeepMind’s latest advancement in text-to-video generation, pushing the boundaries of what AI can create from natural language prompts. With native audio generation, improved prompt adherence, and stunning realism, Veo3 is redefining multimedia content creation.
>
>
> 🔥 **Key Features**
>
> Text to Image and Video: Generate high-fidelity visuals with cinematic detail directly from your text prompts.
>
> Native Audio Generation: Add ambient noise, sound effects, and dialogue that sync naturally with visuals—no post-production needed.
>
> Dialogue & Lip-Sync: Generate characters speaking your script with accurate lip-sync, opening doors to AI filmmaking and animated storytelling.
>
> Game World Creation: Build immersive video game environments from just a sentence—Veo3’s spatial and physics understanding is a game-changer.
>
> High Prompt Accuracy: Grounded in real-world physics and enhanced by deep prompt comprehension, Veo3 delivers consistent and context-aware outputs.
>
> Cinematic Quality: Output videos in stunning quality, complete with smooth motion and realistic effects
>
>
> **Built by Google DeepMind**
>
> Trained by world-class researchers at Google DeepMind, Veo3 is engineered for creators, developers, and visionaries looking to push the limits of AI-generated content.
>
> ✨ Prompting Tips (from Google's Guide)
> To get the best results, try these prompt strategies:
>
> Shot Composition: "Close-up," "two shot," "over-the-shoulder"
>
> Lens & Focus: "Macro lens," "shallow focus," "wide-angle lens"
>
> Genre & Style: "Sci-fi," "romantic comedy," "action movie"
>
> Camera Motion: "Zoom shot," "dolly shot," "tracking shot," "pan shot"
>
> **Example Prompt:**
>
> Close up shot (composition) of melting icicles (subject) on a frozen rock wall (context) with cool blue tones (ambiance), zoomed in (camera motion) maintaining close-up detail of water drips (action).

