## Basic model info

Model name: bytedance/seedream-3
Model description: A text-to-image model with support for native high-resolution (2K) image generation


## Model inputs

- prompt (required): Text prompt for image generation (string)
- seed (optional): Random seed. Set for reproducible generation (integer)
- aspect_ratio (optional): Image aspect ratio. Set to 'custom' to specify width and height. (string)
- size (optional): Big images will have their longest dimension be 2048px. Small images will have their shortest dimension be 512px. Regular images will always be 1 megapixel. Ignored if aspect ratio is custom. (string)
- width (optional): Image width (integer)
- height (optional): Image height (integer)
- guidance_scale (optional): Prompt adherence. Higher = more literal. (number)

## Input Schema

{
  "type": "object",
  "title": "Input",
  "required": [
    "prompt"
  ],
  "properties": {
    "seed": {
      "type": "integer",
      "title": "Seed",
      "x-order": 1,
      "nullable": true,
      "description": "Random seed. Set for reproducible generation"
    },
    "size": {
      "enum": [
        "small",
        "regular",
        "big"
      ],
      "type": "string",
      "title": "size",
      "description": "Big images will have their longest dimension be 2048px. Small images will have their shortest dimension be 512px. Regular images will always be 1 megapixel. Ignored if aspect ratio is custom.",
      "default": "regular",
      "x-order": 3
    },
    "width": {
      "type": "integer",
      "title": "Width",
      "default": 2048,
      "maximum": 2048,
      "minimum": 512,
      "x-order": 4,
      "description": "Image width"
    },
    "height": {
      "type": "integer",
      "title": "Height",
      "default": 2048,
      "maximum": 2048,
      "minimum": 512,
      "x-order": 5,
      "description": "Image height"
    },
    "prompt": {
      "type": "string",
      "title": "Prompt",
      "x-order": 0,
      "description": "Text prompt for image generation"
    },
    "aspect_ratio": {
      "enum": [
        "1:1",
        "3:4",
        "4:3",
        "16:9",
        "9:16",
        "2:3",
        "3:2",
        "21:9",
        "custom"
      ],
      "type": "string",
      "title": "aspect_ratio",
      "description": "Image aspect ratio. Set to 'custom' to specify width and height.",
      "default": "16:9",
      "x-order": 2
    },
    "guidance_scale": {
      "type": "number",
      "title": "Guidance Scale",
      "default": 2.5,
      "maximum": 10,
      "minimum": 1,
      "x-order": 6,
      "description": "Prompt adherence. Higher = more literal."
    }
  }
}

## Model output schema

{
  "type": "string",
  "title": "Output",
  "format": "uri"
}

If the input or output schema includes a format of URI, it is referring to a file.


## Example inputs and outputs

Use these example outputs to better understand the types of inputs the model accepts, and the types of outputs the model returns:

### Example (https://replicate.com/p/n60n6q2r4hrma0cqmtcsp3ggsc)

#### Input

```json
{
  "size": "regular",
  "width": 2048,
  "height": 2048,
  "prompt": "A cinematic, photorealistic medium shot capturing the nostalgic warmth of a mid-2000s indie film. The focus is a young woman with a sleek, straight bob haircut in cool platinum white with freckled skin, looking directly and intently into the camera lens with a knowing smirk, her head is looking up slightly. She wears an oversized band t-shirt that says \"Seedream 3.0 on Replicate\" in huge stylized text over a long-sleeved striped top and simple silver stud earrings. The lighting is soft, golden hour sunlight creating lens flare and illuminating dust motes in the air. The background shows a blurred outdoor urban setting with graffiti-covered walls (the graffiti says \"seedream\" in stylized graffiti lettering), rendered with a shallow depth of field. Natural film grain, a warm, slightly muted color palette, and sharp focus on her expressive eyes enhance the intimate, authentic feel",
  "aspect_ratio": "16:9",
  "guidance_scale": 2.5
}
```

#### Output

```json
"https://replicate.delivery/xezq/3o7diQSJVj78DlUsUlqk75pJOvR6ApeCdTmXQED4xJ0WeX6UA/tmpcoezojc2.jpg"
```


## Model readme

> # Seedream-3.0 (text-to-image)
>
> Seedream 3.0 is a bilingual (Chinese and English) text-to-image model developed by ByteDance's large model team, supporting native high-resolution image generation. Seedream 3.0 offers significant improvements: native 2K resolution output, faster response times, more accurate small text generation, enhanced text layout, strong instruction following, improved aesthetics and structure, and better fidelity and detail. It leads in multiple evaluations and can be applied to more complex and diverse image generation scenarios.
>
> ## Capabilities
>
> With significantly improved overall capabilities, the model leads the field. It excels in text-image alignment, composition, and aesthetic quality, consistently ranking first in benchmarks such as EvalMuse, HPSv2, and MPS.
>
> Exceptional Text Layout for Visually Stunning Results: The model excels at generating small and large text, particularly in Chinese and English, with high accuracy and aesthetically pleasing layouts. Easily create designer-quality posters incorporating diverse fonts, styles, and layouts, surpassing even the human-designed templates of platforms like Canva.
>
> Immersive Visuals with Photorealistic Portraits and Cinematic Beauty: Experience significantly enhanced image aesthetics, especially in cinematic scenes. Generated portraits are more realistic with improved skin and hair textures and highly detailed clothing.
>
> Efficient Generation with Native 2K High Resolution: Generate images in native 2K resolution with various aspect ratios, eliminating the need for post-processing. Leveraging multiple model acceleration techniques, a 1K image can be generated in just 3 seconds, significantly faster than other models.
>
> ## Applications
>
> Seedream 3.0 has broad applications across e-commerce, gaming, film and television, animation, and design, revolutionizing traditional content creation and dramatically increasing the efficiency of visual content production.

