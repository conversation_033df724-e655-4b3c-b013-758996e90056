## Basic model info

Model name: black-forest-labs/flux-kontext-pro
Model description: A state-of-the-art text-based image editing model that delivers high-quality outputs with excellent prompt following and consistent results for transforming images through natural language


## Model inputs

- prompt (required): Text description of what you want to generate, or the instruction on how to edit the given image. (string)
- input_image (optional): Image to use as reference. Must be jpeg, png, gif, or webp. (string)
- aspect_ratio (optional): Aspect ratio of the generated image. Use 'match_input_image' to match the aspect ratio of the input image. (string)
- seed (optional): Random seed. Set for reproducible generation (integer)
- output_format (optional): Output format for the generated image (string)
- safety_tolerance (optional): Safety tolerance, 0 is most strict and 6 is most permissive. 2 is currently the maximum allowed when input images are used. (integer)

## Input schema

{
  "type": "object",
  "title": "Input",
  "required": [
    "prompt"
  ],
  "properties": {
    "seed": {
      "type": "integer",
      "title": "Seed",
      "x-order": 3,
      "nullable": true,
      "description": "Random seed. Set for reproducible generation"
    },
    "prompt": {
      "type": "string",
      "title": "Prompt",
      "x-order": 0,
      "description": "Text description of what you want to generate, or the instruction on how to edit the given image."
    },
    "input_image": {
      "type": "string",
      "title": "Input Image",
      "format": "uri",
      "x-order": 1,
      "nullable": true,
      "description": "Image to use as reference. Must be jpeg, png, gif, or webp."
    },
    "aspect_ratio": {
      "enum": [
        "match_input_image",
        "1:1",
        "16:9",
        "9:16",
        "4:3",
        "3:4",
        "3:2",
        "2:3",
        "4:5",
        "5:4",
        "21:9",
        "9:21",
        "2:1",
        "1:2"
      ],
      "type": "string",
      "title": "aspect_ratio",
      "description": "Aspect ratio of the generated image. Use 'match_input_image' to match the aspect ratio of the input image.",
      "default": "match_input_image",
      "x-order": 2
    },
    "output_format": {
      "enum": [
        "jpg",
        "png"
      ],
      "type": "string",
      "title": "output_format",
      "description": "Output format for the generated image",
      "default": "png",
      "x-order": 4
    },
    "safety_tolerance": {
      "type": "integer",
      "title": "Safety Tolerance",
      "default": 2,
      "maximum": 6,
      "minimum": 0,
      "x-order": 5,
      "description": "Safety tolerance, 0 is most strict and 6 is most permissive. 2 is currently the maximum allowed when input images are used."
    }
  }
}

## Model output schema

{
  "type": "string",
  "title": "Output",
  "format": "uri"
}

If the input or output schema includes a format of URI, it is referring to a file.


## Example inputs and outputs

Use these example outputs to better understand the types of inputs the model accepts, and the types of outputs the model returns:

### Example (https://replicate.com/p/ks1w6tyk9nrma0cq6ycacv92xm)

#### Input

```json
{
  "prompt": "Make this a 90s cartoon",
  "input_image": "https://replicate.delivery/pbxt/N55l5TWGh8mSlNzW8usReoaNhGbFwvLeZR3TX1NL4pd2Wtfv/replicate-prediction-f2d25rg6gnrma0cq257vdw2n4c.png",
  "aspect_ratio": "match_input_image",
  "output_format": "jpg",
  "safety_tolerance": 2
}
```

#### Output

```json
"https://replicate.delivery/xezq/83OKs6yfdoT5YCpfREnrFFbqLbfWbus8Q0e06fQ0BAMDRKamC/tmpu3nqollf.jpg"
```


## Model readme

> # FLUX.1 Kontext - Text-Based Image Editing
>
> FLUX.1 Kontext is a state-of-the-art image editing model from Black Forest Labs that allows you to edit images using text prompts. It's the best in class for text-guided image editing and offers superior results compared to other models like OpenAI's 4o/gpt-image-1.
>
> ## Available Models
>
> - **[FLUX.1 Kontext [dev]](https://replicate.com/black-forest-labs/flux-kontext-dev)**: Open-weight version with non-commercial license (commercial use available through Replicate)
> - **[FLUX.1 Kontext [pro]](https://replicate.com/black-forest-labs/flux-kontext-pro)**: State-of-the-art performance with high-quality outputs, great prompt following, and consistent results
> - **[FLUX.1 Kontext [max]](https://replicate.com/black-forest-labs/flux-kontext-max)**: Premium model with maximum performance and improved typography generation
>
> ## What You Can Do
>
> Kontext excels at:
>
> - **Style Transfer**: Convert photos to different art styles (watercolor, oil painting, sketches)
> - **Object/Clothing Changes**: Modify hairstyles, add accessories, change colors
> - **Text Editing**: Replace text in signs, posters, and labels
> - **Background Swapping**: Change environments while preserving subjects
> - **Character Consistency**: Maintain identity across multiple edits
>
> ## Prompting Best Practices
>
> ### Be Specific
>
> - Use clear, detailed language with exact colors and descriptions
> - Avoid vague terms like "make it better"
> - Name subjects directly: "the woman with short black hair" vs. "she"
>
> ### Preserve Intentionally
>
> - Specify what should stay the same: "while keeping the same facial features"
> - Use "maintain the original composition" to preserve layout
> - For background changes: "Change the background to a beach while keeping the person in the exact same position"
>
> ### Text Editing Tips
>
> - Use quotation marks: "replace 'old text' with 'new text'"
> - Stick to readable fonts
> - Match text length when possible to preserve layout
>
> ### Style Transfer
>
> - Be specific about artistic styles: "impressionist painting" not "artistic"
> - Reference known movements: "Renaissance" or "1960s pop art"
> - Describe key traits: "visible brushstrokes, thick paint texture"
>
> ### Complex Edits
>
> - Break into smaller steps for better results
> - Start simple and iterate
> - Use descriptive action verbs instead of "transform" for more control
>
> ## Commercial Use
>
> When using FLUX.1 Kontext on Replicate, you're free to use outputs commercially in apps, marketing, or any business use.
>
> ## Example Applications
>
> Check out these [specialized apps](https://replicate.com/flux-kontext-apps) built with Kontext:
>
> - [Portrait series](https://replicate.com/flux-kontext-apps/portrait-series): Generate portrait variations from a single image
> - [Change haircut](https://replicate.com/flux-kontext-apps/change-haircut): Modify hairstyles and colors
> - [Iconic locations](https://replicate.com/flux-kontext-apps/iconic-locations): Place subjects in famous landmarks
> - [Professional headshot](https://replicate.com/flux-kontext-apps/professional-headshot): Create professional portraits
>
> ## Tips Summary
>
> - **Be specific** with colors, styles, and descriptions
> - **Start simple** and iterate on successful edits
> - **Preserve intentionally** by stating what to keep unchanged
> - **Use quotation marks** for exact text replacements
> - **Control composition** by specifying camera angles and framing
> - **Choose verbs carefully** - "change" vs "transform" gives different results

