## Basic model info

Model name: tencent/hunyuan-video
Model description: A state-of-the-art text-to-video generation model capable of creating high-quality videos with realistic motion from text descriptions


## Model inputs

- prompt (optional): The prompt to guide the video generation (string)
- width (optional): Width of the video in pixels (must be divisible by 16) (integer)
- height (optional): Height of the video in pixels (must be divisible by 16) (integer)
- video_length (optional): Number of frames to generate (must be 4k+1, ex: 49 or 129) (integer)
- infer_steps (optional): Number of denoising steps (integer)
- embedded_guidance_scale (optional): Guidance scale (number)
- fps (optional): Frames per second of the output video (integer)
- seed (optional): Random seed (leave empty for random) (integer)

## Input schema

{
  "type": "object",
  "title": "Input",
  "properties": {
    "fps": {
      "type": "integer",
      "title": "Fps",
      "default": 24,
      "minimum": 1,
      "x-order": 6,
      "description": "Frames per second of the output video"
    },
    "seed": {
      "type": "integer",
      "title": "Seed",
      "x-order": 7,
      "description": "Random seed (leave empty for random)"
    },
    "width": {
      "type": "integer",
      "title": "Width",
      "default": 864,
      "maximum": 1280,
      "minimum": 16,
      "x-order": 1,
      "description": "Width of the video in pixels (must be divisible by 16)"
    },
    "height": {
      "type": "integer",
      "title": "Height",
      "default": 480,
      "maximum": 1280,
      "minimum": 16,
      "x-order": 2,
      "description": "Height of the video in pixels (must be divisible by 16)"
    },
    "prompt": {
      "type": "string",
      "title": "Prompt",
      "default": "A cat walks on the grass, realistic style",
      "x-order": 0,
      "description": "The prompt to guide the video generation"
    },
    "infer_steps": {
      "type": "integer",
      "title": "Infer Steps",
      "default": 50,
      "minimum": 1,
      "x-order": 4,
      "description": "Number of denoising steps"
    },
    "video_length": {
      "type": "integer",
      "title": "Video Length",
      "default": 129,
      "maximum": 200,
      "minimum": 1,
      "x-order": 3,
      "description": "Number of frames to generate (must be 4k+1, ex: 49 or 129)"
    },
    "embedded_guidance_scale": {
      "type": "number",
      "title": "Embedded Guidance Scale",
      "default": 6,
      "maximum": 10,
      "minimum": 1,
      "x-order": 5,
      "description": "Guidance scale"
    }
  }
}

## Model output schema

{
  "type": "string",
  "title": "Output",
  "format": "uri"
}

If the input or output schema includes a format of URI, it is referring to a file.


## Example inputs and outputs

Use these example outputs to better understand the types of inputs the model accepts, and the types of outputs the model returns:

### Example (https://replicate.com/p/qrxsmv0bc5rm80cmc3bt6bfe78)

#### Input

```json
{
  "fps": 24,
  "width": 864,
  "height": 480,
  "prompt": "Dynamic shot racing alongside a steam locomotive on mountain tracks, camera panning from wheels to steam billowing against snow-capped peaks. Epic scale, dramatic lighting, photorealistic detail.",
  "infer_steps": 50,
  "video_length": 129,
  "embedded_guidance_scale": 6
}
```

#### Output

```json
"https://replicate.delivery/xezq/TnvddqDCvU5aItZlwvCZkoGZ2PajYDhiEzucZG1a2lLQIMBF/output_50014.mp4"
```


## Model readme

> # HunyuanVideo Text-to-Video Generation Model 🎬
>
> HunyuanVideo is an advanced text-to-video generation model that can create high-quality videos from text descriptions. It features a comprehensive framework that integrates image-video joint model training and efficient infrastructure for large-scale model training and inference.
>
> This deployment is parallelized across multiple GPUs using context parallel attention from the awesome [ParaAttention](https://github.com/chengzeyi/ParaAttention) repo.
>
> ## Model Description ✨
>
> This model is trained on a spatial-temporally compressed latent space and uses a large language model for text encoding. According to professional human evaluation results, HunyuanVideo outperforms previous state-of-the-art models in terms of text alignment, motion quality, and visual quality.
>
> Key features:
>
> - 🎨 High-quality video generation from text descriptions
> - 📐 Support for various aspect ratios and resolutions
> - ✍️ Advanced prompt handling with a built-in rewrite system
> - 🎯 Stable motion generation and temporal consistency
>
> ## Predictions Examples 💫
>
> The model works well for prompts like:
> - "A cat walks on the grass, realistic style"
> - "A drone shot of mountains at sunset"
> - "A flower blooming in timelapse"
>
> ## Limitations ⚠️
>
> - Generation time increases with video length and resolution
> - Higher resolutions require more GPU memory
> - Some complex motions may require prompt engineering for best results
>
> ## Citation 📚
>
> If you use this model in your research, please cite:
> ```bibtex
> @misc{kong2024hunyuanvideo,
>       title={HunyuanVideo: A Systematic Framework For Large Video Generative Models},
>       author={Weijie Kong, et al.},
>       year={2024},
>       archivePrefix={arXiv},
>       primaryClass={cs.CV}
> }
> ```

