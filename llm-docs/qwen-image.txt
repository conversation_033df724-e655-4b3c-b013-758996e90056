## Basic model info

Model name: qwen/qwen-image
Model description: An image generation foundation model in the Qwen series that achieves significant advances in complex text rendering.


## Model inputs

- prompt (required): Prompt for generated image (string)
- enhance_prompt (optional): Enhance the prompt with positive magic. (boolean)
- negative_prompt (optional): Negative prompt for generated image (string)
- aspect_ratio (optional): Aspect ratio for the generated image (string)
- image_size (optional): Image size for the generated image (string)
- go_fast (optional): Run faster predictions with additional optimizations. (boolean)
- num_inference_steps (optional): Number of denoising steps. Recommended range is 28-50, and lower number of steps produce lower quality outputs, faster. (integer)
- guidance (optional): Guidance for generated image. Lower values can give more realistic images. Good values to try are 2, 2.5, 3 and 3.5 (number)
- seed (optional): Random seed. Set for reproducible generation (integer)
- output_format (optional): Format of the output images (string)
- output_quality (optional): Quality when saving the output images, from 0 to 100. 100 is best quality, 0 is lowest quality. Not relevant for .png outputs (integer)
- disable_safety_checker (optional): Disable safety checker for generated images. (boolean)
- lora_weights (optional): Load LoRA weights. Supports Replicate models in the format <owner>/<username> or <owner>/<username>/<version>, HuggingFace URLs in the format huggingface.co/<owner>/<model-name>, CivitAI URLs in the format civitai.com/models/<id>[/<model-name>], or arbitrary .safetensors URLs from the Internet. For example, 'fofr/flux-pixar-cars' (string)
- lora_scale (optional): Determines how strongly the main LoRA should be applied. Sane results between 0 and 1 for base inference. For go_fast we apply a 1.5x multiplier to this value; we've generally seen good performance when scaling the base value by that amount. You may still need to experiment to find the best value for your particular lora. (number)


## Model output schema

{
  "type": "array",
  "items": {
    "type": "string",
    "format": "uri"
  },
  "title": "Output"
}

If the input or output schema includes a format of URI, it is referring to a file.


## Example inputs and outputs

Use these example outputs to better understand the types of inputs the model accepts, and the types of outputs the model returns:

### Example (https://replicate.com/p/q75tcckzasrm80crerxscfah0r)

#### Input

```json
{
  "prompt": "A dynamic portrait photo of a woman",
  "go_fast": true,
  "guidance": 4,
  "aspect_ratio": "16:9",
  "output_format": "webp",
  "output_quality": 80,
  "num_inference_steps": 50
}
```

#### Output

```json
[
  "https://replicate.delivery/xezq/zjUG6jUHJJ7eSKgV397UUf7K6po7NPujMU34kGjJ9znnaqHVA/out-0.webp"
]
```


## Model readme

> ## Introduction
>
> We are thrilled to release **Qwen-Image**, an image generation foundation model in the Qwen series that achieves significant advances in **complex text rendering** and **precise image editing**. Experiments show strong general capabilities in both image generation and editing, with exceptional performance in text rendering, especially for Chinese.
>
> ![](https://qianwen-res.oss-cn-beijing.aliyuncs.com/Qwen-Image/bench.png#center)
>
> ## News
> - 2025.08.04: We released the [Technical Report](https://qianwen-res.oss-cn-beijing.aliyuncs.com/Qwen-Image/Qwen_Image.pdf) of Qwen-Image!
> - 2025.08.04: We released Qwen-Image weights! Check at [huggingface](https://huggingface.co/Qwen/Qwen-Image) and [Modelscope](https://modelscope.cn/models/Qwen/Qwen-Image)!
> - 2025.08.04: We released Qwen-Image! Check our [blog](https://qwenlm.github.io/blog/qwen-image) for more details!
>
>
> ## Showcase
>
> One of its standout capabilities is high-fidelity text rendering across diverse images. Whether it’s alphabetic languages like English or logographic scripts like Chinese, Qwen-Image preserves typographic details, layout coherence, and contextual harmony with stunning accuracy. Text isn’t just overlaid—it’s seamlessly integrated into the visual fabric.
>
> ![](https://qianwen-res.oss-cn-beijing.aliyuncs.com/Qwen-Image/s1.jpg#center)
>
> Beyond text, Qwen-Image excels at general image generation with support for a wide range of artistic styles. From photorealistic scenes to impressionist paintings, from anime aesthetics to minimalist design, the model adapts fluidly to creative prompts, making it a versatile tool for artists, designers, and storytellers.
>
> ![](https://qianwen-res.oss-cn-beijing.aliyuncs.com/Qwen-Image/s2.jpg#center)
>
> When it comes to image editing, Qwen-Image goes far beyond simple adjustments. It enables advanced operations such as style transfer, object insertion or removal, detail enhancement, text editing within images, and even human pose manipulation—all with intuitive input and coherent output. This level of control brings professional-grade editing within reach of everyday users.
>
> ![](https://qianwen-res.oss-cn-beijing.aliyuncs.com/Qwen-Image/s3.jpg#center)
>
> But Qwen-Image doesn’t just create or edit—it understands. It supports a suite of image understanding tasks, including object detection, semantic segmentation, depth and edge (Canny) estimation, novel view synthesis, and super-resolution. These capabilities, while technically distinct, can all be seen as specialized forms of intelligent image editing, powered by deep visual comprehension.
>
> ![](https://qianwen-res.oss-cn-beijing.aliyuncs.com/Qwen-Image/s4.jpg#center)
>
> Together, these features make Qwen-Image not just a tool for generating pretty pictures, but a comprehensive foundation model for intelligent visual creation and manipulation—where language, layout, and imagery converge.
>
>
> ## License Agreement
>
> Qwen-Image is licensed under Apache 2.0.
>
> ## Citation
>
> We kindly encourage citation of our work if you find it useful.
>
> ```bibtex
> @article{qwen-image,
>     title={Qwen-Image Technical Report},
>     author={Qwen Team},
>     journal={arXiv preprint},
>     year={2025}
> }
> ```

