## Basic model info

Model name: black-forest-labs/flux-dev
Model description: A 12 billion parameter rectified flow transformer capable of generating images from text descriptions


## Model inputs

- prompt (required): Prompt for generated image (string)
- aspect_ratio (optional): Aspect ratio for the generated image (string)
- image (optional): Input image for image to image mode. The aspect ratio of your output will match this image (string)
- prompt_strength (optional): Prompt strength when using img2img. 1.0 corresponds to full destruction of information in image (number)
- num_outputs (optional): Number of outputs to generate (integer)
- num_inference_steps (optional): Number of denoising steps. Recommended range is 28-50, and lower number of steps produce lower quality outputs, faster. (integer)
- guidance (optional): Guidance for generated image. Lower values can give more realistic images. Good values to try are 2, 2.5, 3 and 3.5 (number)
- seed (optional): Random seed. Set for reproducible generation (integer)
- output_format (optional): Format of the output images (string)
- output_quality (optional): Quality when saving the output images, from 0 to 100. 100 is best quality, 0 is lowest quality. Not relevant for .png outputs (integer)
- disable_safety_checker (optional): Disable safety checker for generated images. (boolean)
- go_fast (optional): Run faster predictions with additional optimizations. (boolean)
- megapixels (optional): Approximate number of megapixels for generated image (string)


## Input schema
{
  "type": "object",
  "title": "Input",
  "required": [
    "prompt"
  ],
  "properties": {
    "seed": {
      "type": "integer",
      "title": "Seed",
      "x-order": 7,
      "description": "Random seed. Set for reproducible generation"
    },
    "image": {
      "type": "string",
      "title": "Image",
      "format": "uri",
      "x-order": 2,
      "description": "Input image for image to image mode. The aspect ratio of your output will match this image"
    },
    "prompt": {
      "type": "string",
      "title": "Prompt",
      "x-order": 0,
      "description": "Prompt for generated image"
    },
    "go_fast": {
      "type": "boolean",
      "title": "Go Fast",
      "default": true,
      "x-order": 11,
      "description": "Run faster predictions with additional optimizations."
    },
    "guidance": {
      "type": "number",
      "title": "Guidance",
      "default": 3,
      "maximum": 10,
      "minimum": 0,
      "x-order": 6,
      "description": "Guidance for generated image. Lower values can give more realistic images. Good values to try are 2, 2.5, 3 and 3.5"
    },
    "megapixels": {
      "enum": [
        "1",
        "0.25"
      ],
      "type": "string",
      "title": "megapixels",
      "description": "Approximate number of megapixels for generated image",
      "default": "1",
      "x-order": 12
    },
    "num_outputs": {
      "type": "integer",
      "title": "Num Outputs",
      "default": 1,
      "maximum": 4,
      "minimum": 1,
      "x-order": 4,
      "description": "Number of outputs to generate"
    },
    "aspect_ratio": {
      "enum": [
        "1:1",
        "16:9",
        "21:9",
        "3:2",
        "2:3",
        "4:5",
        "5:4",
        "3:4",
        "4:3",
        "9:16",
        "9:21"
      ],
      "type": "string",
      "title": "aspect_ratio",
      "description": "Aspect ratio for the generated image",
      "default": "1:1",
      "x-order": 1
    },
    "output_format": {
      "enum": [
        "webp",
        "jpg",
        "png"
      ],
      "type": "string",
      "title": "output_format",
      "description": "Format of the output images",
      "default": "webp",
      "x-order": 8
    },
    "output_quality": {
      "type": "integer",
      "title": "Output Quality",
      "default": 80,
      "maximum": 100,
      "minimum": 0,
      "x-order": 9,
      "description": "Quality when saving the output images, from 0 to 100. 100 is best quality, 0 is lowest quality. Not relevant for .png outputs"
    },
    "prompt_strength": {
      "type": "number",
      "title": "Prompt Strength",
      "default": 0.8,
      "maximum": 1,
      "minimum": 0,
      "x-order": 3,
      "description": "Prompt strength when using img2img. 1.0 corresponds to full destruction of information in image"
    },
    "num_inference_steps": {
      "type": "integer",
      "title": "Num Inference Steps",
      "default": 28,
      "maximum": 50,
      "minimum": 1,
      "x-order": 5,
      "description": "Number of denoising steps. Recommended range is 28-50, and lower number of steps produce lower quality outputs, faster."
    },
    "disable_safety_checker": {
      "type": "boolean",
      "title": "Disable Safety Checker",
      "default": false,
      "x-order": 10,
      "description": "Disable safety checker for generated images."
    }
  }
}

## Model output schema

{
  "type": "array",
  "items": {
    "type": "string",
    "format": "uri"
  },
  "title": "Output"
}

If the input or output schema includes a format of URI, it is referring to a file.


## Example inputs and outputs

Use these example outputs to better understand the types of inputs the model accepts, and the types of outputs the model returns:

### Example (https://replicate.com/p/pab8srw8jhrm20cj1e7s0d8kf4)

#### Input

```json
{
  "seed": 17536,
  "prompt": "womens street skateboarding final in Paris Olympics 2024",
  "go_fast": true,
  "guidance": 3.5,
  "num_outputs": 1,
  "aspect_ratio": "1:1",
  "output_format": "webp",
  "output_quality": 80,
  "prompt_strength": 0.8,
  "num_inference_steps": 28
}
```

#### Output

```json
[
  "https://replicate.delivery/yhqm/LhahE53ikx7ROteqIZ7Bgzc9wIoS76oQU8WLPPeUiCL9BiemA/out-0.webp"
]
```


## Model readme

> ![](https://tjzk.replicate.delivery/markdownx/44d3556c-2848-45d3-8bbb-8be67da8ba3e.jpg)
>
> `FLUX.1 [dev]` is a 12 billion parameter rectified flow transformer capable of generating images from text descriptions.
> For more information, please read our [blog post](https://blackforestlabs.ai/announcing-black-forest-labs/).
>
> # Key Features
> 1. Cutting-edge output quality, second only to our state-of-the-art model `FLUX.1 [pro]`.
> 2. Competitive prompt following, matching the performance of closed source alternatives .
> 3. Trained using guidance distillation, making `FLUX.1 [dev]` more efficient.
> 4. Open weights to drive new scientific research, and empower artists to develop innovative workflows.
> 5. Generated outputs can be used for personal, scientific, and commercial purposes as described in the [flux-1-dev-non-commercial-license](https://huggingface.co/black-forest-labs/FLUX.1-dev/blob/main/LICENSE.md).
>
> # Usage
> We provide a reference implementation of `FLUX.1 [dev]`, as well as sampling code, in a dedicated [github repository](https://github.com/black-forest-labs/flux).
> Developers and creatives looking to build on top of `FLUX.1 [dev]` are encouraged to use this as a starting point.
>
> ## ComfyUI
> `FLUX.1 [dev]` is also available in [Comfy UI](https://github.com/comfyanonymous/ComfyUI) for local inference with a node-based workflow.
>
> # Limitations
> - This model is not intended or able to provide factual information.
> - As a statistical model this checkpoint might amplify existing societal biases.
> - The model may fail to generate output that matches the prompts.
> - Prompt following is heavily influenced by the prompting-style.
>
> # Out-of-Scope Use
> The model and its derivatives may not be used
>
> - In any way that violates any applicable national, federal, state, local or international law or regulation.
> - For the purpose of exploiting, harming or attempting to exploit or harm minors in any way; including but not limited to the solicitation, creation, acquisition, or dissemination of child exploitative content.
> - To generate or disseminate verifiably false information and/or content with the purpose of harming others.
> - To generate or disseminate personal identifiable information that can be used to harm an individual.
> - To harass, abuse, threaten, stalk, or bully individuals or groups of individuals.
> - To create non-consensual nudity or illegal pornographic content.
> - For fully automated decision making that adversely impacts an individual's legal rights or otherwise creates or modifies a binding, enforceable obligation.
> - Generating or facilitating large-scale disinformation campaigns.
>
> # Accelerated Inference
> We provide a `go_fast` flag within the API which toggles a version of flux-schnell optimized for inference. Currently this version is a compiled fp8 quantization with an optimized attention kernel. We'll update the model and this documentation as we develop further enhancements.
>
> # License
> If you generate images on Replicate with FLUX.1 models and their fine-tunes, then you can use the images commercially.
>
> If you download the weights off Replicate and generate images on your own computer, you can't use the images commercially.

