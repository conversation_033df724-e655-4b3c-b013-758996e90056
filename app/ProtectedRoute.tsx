'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { usePBContext } from "@/src/context/PocketbaseContext";
import { useReferral } from "@/src/hooks/use-referral";

interface ProtectedRouteProps {
    children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
    const { user } = usePBContext();
    const router = useRouter();
    
    const isAuthenticated = user != null;

    useEffect(() => {
        if (!isAuthenticated) {
            router.replace('/login');
        }
    }, [isAuthenticated, router]);

    // import for effects
    const _ = useReferral();

    if (!isAuthenticated) {
        return null; // or a loading spinner
    }

    return <>{children}</>;
};

export default ProtectedRoute;
