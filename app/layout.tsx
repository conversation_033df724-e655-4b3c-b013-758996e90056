import { Metadata } from 'next';
import ClientProviders from './ClientProviders';
import "@/src/index.css";
import "@/src/i18n/config";

export const metadata: Metadata = {
  title: 'AI Content Generator',
  description: 'Generate amazing content with AI',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        <ClientProviders>
          {children}
        </ClientProviders>
      </body>
    </html>
  );
}
